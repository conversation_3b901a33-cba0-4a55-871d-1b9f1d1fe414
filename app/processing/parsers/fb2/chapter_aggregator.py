# app/processing/parsers/fb2/chapter_aggregator.py

import logging
import re

from app.processing.canonical_model import CanonicalChapter

from .chapter_state_machine import ChapterStateMachine
from .fb2_model import (
    Section,
)

logger = logging.getLogger(__name__)


class ChapterAggregator:
    """Отвечает за агрегацию глав из FB2 элементов через единый State Machine.

    Инкапсулирует всю логику определения глав: единый ChapterStateMachine для всех случаев,
    рекурсивную обработку секций, фильтрацию служебных разделов.
    Обеспечивает детерминированный результат независимо от структуры данных.
    """

    # Константа для фильтрации служебных разделов
    FOOTER_STOP_TITLES = {
        "notabene",
        "nota bene",
    }

    def __init__(
        self,
        min_chapters_threshold: int = 4,
        min_chapter_length: int = 200,
        markdown_renderer=None,
    ):
        """Инициализирует агрегатор глав.

        Args:
            min_chapters_threshold: Минимальный порог глав для применения эвристик
            min_chapter_length: Минимальная длина главы в символах текстового контента
            markdown_renderer: Инстанс MarkdownRenderer для преобразования контента
        """
        self.min_chapters_threshold = min_chapters_threshold
        self.min_chapter_length = min_chapter_length
        self.markdown_renderer = markdown_renderer

        # Регулярные выражения перенесены в MarkerAnalyzer
        # State Machine обрабатывает логику определения глав

        # Диагностическая информация о последней использованной эвристике
        self._last_used_heuristic: str | None = None
        # Ссылка на последний успешный State Machine для получения маркеров
        self._last_successful_state_machine: ChapterStateMachine | None = None

    def get_last_used_heuristic(self) -> str | None:
        """Возвращает название последней использованной эвристики для диагностики.

        Returns:
            Название эвристики или None если не было вызовов aggregate_chapters_from_elements
        """
        return self._last_used_heuristic

    def get_detailed_heuristic_info(self) -> str:
        """Возвращает детализированную информацию о стратегии и маркерах.

        Формат: "СТРАТЕГИЯ=МАРКЕР1,МАРКЕР2" или "unknown" если информация недоступна.

        Returns:
            Строка с детализированной информацией о стратегии и использованных маркерах
        """
        if not self._last_used_heuristic:
            return "unknown"

        # Получаем использованные маркеры из последнего успешного State Machine
        markers: set[str] = set()
        if self._last_successful_state_machine:
            markers = self._last_successful_state_machine.get_used_marker_types()

        markers_str = ",".join(sorted(markers)) if markers else "no_markers"
        return f"{self._last_used_heuristic}={markers_str}"

    def aggregate_chapters_from_elements(self, root_elements: list) -> list[CanonicalChapter]:
        """КАСКАДНЫЙ АЛГОРИТМ определения глав с применением стратегий по приоритету.

        Каскадный алгоритм:
        1. Пробуем структурные маркеры (Section, Subtitle + числовые заголовки <strong>1</strong>) - если достаточно глав, СТОП
        2. Пробуем строгие паттерны ("Глава N", "Пролог") - если достаточно глав, СТОП
        3. Пробуем эвристические заголовки - если достаточно глав, СТОП
        4. Применяем глубокое разбиение как последний шанс - если достаточно глав, СТОП
        5. Пробуем разбиение по <subtitle>* * *</subtitle> (финальная эвристика) - если достаточно глав, СТОП

        Args:
            root_elements: Корневые элементы для обработки (могут включать Section)
        """
        # Сбрасываем диагностическую информацию только если она еще не установлена
        # Это предотвращает перезапись успешной стратегии при обработке нескольких body
        if self._last_used_heuristic is None:
            self._last_successful_state_machine = None

        logger.debug("🎯 Применяем каскадный алгоритм определения глав")

        # Стратегия 1: Структурные маркеры (Section, Subtitle)
        chapters, state_machine = self._try_strategy("structural", root_elements)
        # Применяем фильтрацию маленьких глав перед проверкой порога
        filtered_chapters = self.filter_small_chapters(chapters)
        if len(filtered_chapters) >= self.min_chapters_threshold:
            logger.debug(
                f"✅ Структурная стратегия: {len(filtered_chapters)} глав >= {self.min_chapters_threshold} (после фильтрации)"
            )
            self._last_used_heuristic = "STRUCTURAL"
            self._last_successful_state_machine = state_machine
            return filtered_chapters

        # Стратегия 2: Строгие паттерны ("Глава N", "Пролог")
        chapters, state_machine = self._try_strategy("strict", root_elements)
        # Применяем фильтрацию маленьких глав перед проверкой порога
        filtered_chapters = self.filter_small_chapters(chapters)
        if len(filtered_chapters) >= self.min_chapters_threshold:
            logger.debug(
                f"✅ Строгая стратегия: {len(filtered_chapters)} глав >= {self.min_chapters_threshold} (после фильтрации)"
            )
            self._last_used_heuristic = "STRICT_PATTERNS"
            self._last_successful_state_machine = state_machine
            return filtered_chapters

        # Стратегия 3: Эвристические заголовки
        chapters, state_machine = self._try_strategy("heuristic", root_elements)
        # Применяем фильтрацию маленьких глав перед проверкой порога
        filtered_chapters = self.filter_small_chapters(chapters)
        if len(filtered_chapters) >= self.min_chapters_threshold:
            logger.debug(
                f"✅ Эвристическая стратегия: {len(filtered_chapters)} глав >= {self.min_chapters_threshold} (после фильтрации)"
            )
            self._last_used_heuristic = "HEURISTIC"
            self._last_successful_state_machine = state_machine
            return filtered_chapters

        # Стратегия 4: Глубокое разбиение (последний шанс)
        logger.debug(f"🔄 Все стратегии дали < {self.min_chapters_threshold} глав, применяем глубокое разбиение...")
        enhanced_chapters = self._apply_deep_processing(chapters)
        if len(enhanced_chapters) > len(chapters):
            logger.debug(f"✅ Глубокое разбиение: {len(chapters)} → {len(enhanced_chapters)} глав")
            self._last_used_heuristic = "DEEP_SPLIT"
            # Для глубокого разбиения сохраняем последний state_machine (если есть)
            return enhanced_chapters

        # Стратегия 5: Разбиение по subtitle звездным разделителям (финальная эвристика)
        logger.debug("🔄 Даже глубокое разбиение дало недостаточно глав, пробуем subtitle звездные разделители...")
        subtitle_star_chapters, subtitle_star_state_machine = self._try_strategy(
            "subtitle_star_separator", root_elements
        )
        # Применяем фильтрацию маленьких глав перед проверкой порога
        filtered_subtitle_star_chapters = self.filter_small_chapters(subtitle_star_chapters)
        if len(filtered_subtitle_star_chapters) >= self.min_chapters_threshold:
            logger.debug(
                f"✅ Стратегия subtitle звездных разделителей: {len(filtered_subtitle_star_chapters)} глав >= {self.min_chapters_threshold} (после фильтрации)"
            )
            self._last_used_heuristic = "SUBTITLE_STAR_SEPARATOR"
            self._last_successful_state_machine = subtitle_star_state_machine
            return filtered_subtitle_star_chapters

        # Определяем лучший результат из всех стратегий
        best_chapters = chapters
        best_state_machine = state_machine
        best_heuristic = None

        # Сравниваем с результатом subtitle_star_separator (уже отфильтрованным)
        if len(filtered_subtitle_star_chapters) > len(best_chapters):
            best_chapters = filtered_subtitle_star_chapters
            best_state_machine = subtitle_star_state_machine
            best_heuristic = "SUBTITLE_STAR_SEPARATOR"

        # Сравниваем с результатом deep_split
        if len(enhanced_chapters) > len(best_chapters):
            best_chapters = enhanced_chapters
            best_heuristic = "DEEP_SPLIT"

        # Fallback: возвращаем лучший результат
        logger.debug(f"⚠️ Fallback: возвращаем лучший результат ({len(best_chapters)} глав)")
        if not self._last_used_heuristic:
            self._last_used_heuristic = best_heuristic or "FALLBACK"
        # Для fallback сохраняем лучший state_machine только если его еще нет
        if not self._last_successful_state_machine:
            self._last_successful_state_machine = best_state_machine
        return best_chapters

    def _try_strategy(
        self, strategy_mode: str, root_elements: list
    ) -> tuple[list[CanonicalChapter], ChapterStateMachine]:
        """Пробует конкретную стратегию определения глав.

        Args:
            strategy_mode: Режим стратегии ("structural", "strict", "heuristic", "subtitle_star_separator")
            root_elements: Элементы для обработки

        Returns:
            Кортеж из списка найденных глав и State Machine для получения маркеров
        """
        logger.debug(f"🔍 Пробуем стратегию: {strategy_mode}")

        # Создаём State Machine с конкретной стратегией
        state_machine = ChapterStateMachine(markdown_renderer=self.markdown_renderer, strategy_mode=strategy_mode)

        # Для структурной стратегии и subtitle_star_separator обрабатываем элементы как есть
        if strategy_mode == "structural" or strategy_mode == "subtitle_star_separator":
            for element in root_elements:
                state_machine.process_element(element)
        else:
            # Для не-структурных стратегий разворачиваем содержимое секций
            for element in root_elements:
                if hasattr(element, "content"):
                    # Обрабатываем содержимое секции
                    for content_item in element.content:
                        state_machine.process_element(content_item)
                else:
                    state_machine.process_element(element)

        # Получаем результат
        chapters = state_machine.finalize()

        # Логируем результат
        used_markers = state_machine.get_used_marker_types()
        markers_str = ",".join(sorted(used_markers)) if used_markers else "no_markers"
        logger.debug(f"🔍 Стратегия {strategy_mode}: {len(chapters)} глав, маркеры: {markers_str}")

        return chapters, state_machine

    def _apply_deep_processing(self, chapters: list[CanonicalChapter]) -> list[CanonicalChapter]:
        """Применяет глубокую обработку для разбиения больших глав на подглавы.

        Анализирует содержимое глав и ищет текстовые маркеры заголовков
        для дополнительного разбиения.

        Args:
            chapters: Исходный список глав

        Returns:
            Расширенный список глав после разбиения
        """
        enhanced_chapters = []
        deep_split_state_machine = None

        for chapter in chapters:
            # Если глава большая, пытаемся разбить её
            if len(chapter.content_elements) > 50:
                split_chapters, state_machine = self._split_chapter_by_text_markers(chapter)
                enhanced_chapters.extend(split_chapters)
                # Сохраняем State Machine от глубокого разбиения
                if state_machine and state_machine.get_used_marker_types():
                    deep_split_state_machine = state_machine
            else:
                enhanced_chapters.append(chapter)

        # Сохраняем State Machine для диагностики
        if deep_split_state_machine:
            self._last_successful_state_machine = deep_split_state_machine

        return enhanced_chapters

    def _split_chapter_by_text_markers(
        self, chapter: CanonicalChapter
    ) -> tuple[list[CanonicalChapter], ChapterStateMachine]:
        """Разбивает главу по текстовым маркерам заголовков.

        Ищет параграфы, которые соответствуют паттернам заголовков глав,
        и создает новые главы на их основе.

        Args:
            chapter: Глава для разбиения

        Returns:
            Кортеж из списка глав после разбиения и State Machine
        """
        # Используем State Machine с эвристической стратегией для глубокого разбиения
        state_machine = ChapterStateMachine(markdown_renderer=self.markdown_renderer, strategy_mode="heuristic")

        # Обрабатываем элементы главы через State Machine
        for element in chapter.content_elements:
            state_machine.process_element(element)

        # Получаем результат разбиения
        split_chapters = state_machine.finalize()

        # Если разбиение дало больше одной главы, возвращаем результат
        if len(split_chapters) > 1:
            return split_chapters, state_machine

        # Иначе возвращаем исходную главу
        return [chapter], state_machine

    def filter_service_chapters(self, chapters: list[CanonicalChapter]) -> list[CanonicalChapter]:
        """Фильтрует служебные разделы по заголовкам."""
        filtered_chapters = []
        removed_count = 0

        for chapter in chapters:
            # Нормализуем заголовок: приводим к нижнему регистру и убираем пробелы
            normalized_title = re.sub(r"\s+", "", chapter.title.lower())

            # Проверяем, является ли заголовок служебным
            is_service = normalized_title in self.FOOTER_STOP_TITLES or any(
                normalized_title.startswith(stop_title) for stop_title in self.FOOTER_STOP_TITLES
            )

            if is_service:
                logger.debug(f"Исключена служебная глава: '{chapter.title}'")
                removed_count += 1
            else:
                filtered_chapters.append(chapter)

        if removed_count > 0:
            logger.info(f"✂️ Отфильтровано {removed_count} служебных глав")

        return filtered_chapters

    def filter_service_sections(self, sections: list) -> list:
        """Фильтрует служебные секции полностью."""
        filtered_sections = []
        removed_count = 0

        for section in sections:
            if isinstance(section, Section) and self._is_service_section(section):
                removed_count += 1
            else:
                filtered_sections.append(section)

        if removed_count > 0:
            logger.info(f"🗑️  Удалено {removed_count} служебных секций полностью")

        return filtered_sections

    def flatten_section_elements(self, section: Section) -> list:
        """Рекурсивно разворачивает секцию в плоский список всех элементов."""
        elements: list = []

        # Добавляем заголовок секции как маркер
        if section.title:
            elements.append(section.title)

        # Добавляем эпиграфы
        if section.epigraphs:
            elements.extend(section.epigraphs)

        # Обрабатываем контент
        for item in section.content:
            if isinstance(item, Section):
                # Рекурсивно разворачиваем вложенные секции
                elements.extend(self.flatten_section_elements(item))
            else:
                elements.append(item)
        return elements

    def _is_service_section(self, section: Section) -> bool:
        """Проверяет, является ли секция служебной по заголовку."""
        if not section.title or not section.title.elements:
            return False

        # Извлекаем текст заголовка секции
        title_text = ""
        for element in section.title.elements:
            if hasattr(element, "content"):
                # Paragraph элемент
                for content_part in element.content:
                    if isinstance(content_part, str):
                        title_text += content_part
                    elif hasattr(content_part, "text"):
                        text_attr = getattr(content_part, "text", "")
                        if text_attr and isinstance(text_attr, str):
                            title_text += text_attr
            elif isinstance(element, str):
                title_text += element

        # Нормализуем заголовок: приводим к нижнему регистру и убираем пробелы
        normalized_title = re.sub(r"\s+", "", title_text.lower())

        # Проверяем, является ли заголовок служебным
        is_service = normalized_title in self.FOOTER_STOP_TITLES or any(
            normalized_title.startswith(stop_title) for stop_title in self.FOOTER_STOP_TITLES
        )

        if is_service:
            logger.debug(f"Исключена служебная секция: '{title_text}'")

        return is_service

    def filter_small_chapters(self, chapters: list[CanonicalChapter]) -> list[CanonicalChapter]:
        """Фильтрует главы с недостаточным количеством текстового контента.

        Удаляет главы, содержащие менее min_chapter_length символов текстового контента.
        Использует быструю оценку размера без рендеринга Markdown.

        Args:
            chapters: Список глав для фильтрации

        Returns:
            Отфильтрованный список глав
        """
        if not chapters:
            return chapters

        filtered_chapters = []
        removed_count = 0

        for chapter in chapters:
            useful_length = self._calculate_useful_text_length(chapter)

            if useful_length >= self.min_chapter_length:
                filtered_chapters.append(chapter)
            else:
                logger.debug(f"Удалена маленькая глава: '{chapter.title}' ({useful_length} символов)")
                removed_count += 1

        if removed_count > 0:
            logger.debug(f"Отфильтровано {removed_count} маленьких глав")

        return filtered_chapters

    def _calculate_useful_text_length(self, chapter: CanonicalChapter) -> int:
        """Подсчитывает количество текста в главе.

        Использует быструю оценку длины контента без рендеринга Markdown.

        Args:
            chapter: Глава для анализа

        Returns:
            Количество символов контента
        """
        return chapter.estimated_content_length
