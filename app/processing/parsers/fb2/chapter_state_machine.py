# app/processing/parsers/fb2/chapter_state_machine.py

import logging
import re
from dataclasses import dataclass
from enum import Enum
from typing import Optional

from app.processing.canonical_model import CanonicalChapter

from .fb2_model import (
    Emphasis,
    EmptyLine,
    Paragraph,
    Section,
    Strong,
    Subtitle,
)

logger = logging.getLogger(__name__)


class ChapterState(Enum):
    """Состояния машины состояний для обработки глав."""

    SEARCHING = "searching"
    IN_CHAPTER = "in_chapter"


@dataclass
class ChapterMarker:
    """Информация о маркере главы с весом уверенности."""

    type: str  # 'STRONG_HEADER', 'SEPARATOR', 'SUBTITLE', etc.
    weight: int  # Очки уверенности (0-100)
    text: str  # Извлеченный текст заголовка


class MarkerAnalyzer:
    """Анализатор FB2-элементов для определения маркеров глав с весовой системой."""

    # Константы регулярных выражений
    BRACKET_PATTERN = r"^\[\d+\]$"  # паттерн для квадратных скобок
    DOUBLE_BRACKET_PATTERN = r"^\[\[\d+\]\]$"  # паттерн для двойных квадратных скобок
    BRACKET_CHAPTER_PATTERN = r"^\[(\d+)\]\s+глава$"  # паттерн для [N] глава
    CHAPTER_BRACKET_PATTERN = r"^chapter\s+\[(\d+)\]$"  # паттерн для chapter [N]

    def __init__(
        self,
        strategy_mode: str = "auto",
        heuristic_weight: int = 75,
        max_header_length: int = 100,
    ):
        """Инициализирует анализатор с паттернами из ChapterAggregator.

        Args:
            strategy_mode: Режим стратегии ("structural", "strict", "heuristic", "auto")
            heuristic_weight: Вес для эвристических заголовков
            max_header_length: Максимальная длина эвристического заголовка
        """
        # Перенесённые паттерны из ChapterAggregator
        self._chapter_patterns = [
            re.compile(r"^(глава|часть|пролог|эпилог|финал)\s*(\d+|\w+)?$", re.IGNORECASE),
            re.compile(r"^\d+\.\s*(глава|часть).*$", re.IGNORECASE),
            re.compile(r"^(chapter|part|prologue|epilogue)\s*(\d+|\w+)?$", re.IGNORECASE),
            re.compile(r"^\*+$"),  # Строки из звездочек как разделители
            re.compile(self.BRACKET_PATTERN),  # Числа в квадратных скобках [1], [2], [16]
            re.compile(self.DOUBLE_BRACKET_PATTERN),  # Числа в двойных квадратных скобках [[1]], [[2]]
            re.compile(self.BRACKET_CHAPTER_PATTERN, re.IGNORECASE),  # [N] глава
            re.compile(self.CHAPTER_BRACKET_PATTERN, re.IGNORECASE),  # chapter [N]
        ]

        # Настройки стратегии
        self.strategy_mode = strategy_mode
        self.heuristic_weight = heuristic_weight
        self.max_header_length = max_header_length

        # Определяем какие типы маркеров включены для каждой стратегии
        self.enable_structural = strategy_mode in ("structural", "auto")
        self.enable_strict_patterns = strategy_mode in (
            "structural",
            "strict",
            "heuristic",
            "auto",
        )  # Включаем для structural!
        self.enable_heuristic_headers = strategy_mode in ("heuristic", "auto")
        self.enable_subtitle_star_separators = strategy_mode in (
            "subtitle_star_separator",
            "auto",
        )

    def analyze(self, element, parent_title_context: str = None) -> Optional[ChapterMarker]:
        """Анализирует элемент и возвращает информацию о маркере или None.

        Применяет стратегии в зависимости от strategy_mode:
        - structural: Section/Subtitle + числовые заголовки <strong>1</strong>
        - strict: только строгие паттерны + числовые заголовки
        - heuristic: строгие паттерны + эвристические заголовки + числовые
        - auto: все стратегии (по умолчанию)

        Args:
            element: FB2-элемент для анализа
            parent_title_context: Заголовок родительской секции для использования в дочерних секциях без заголовка

        Returns:
            ChapterMarker с информацией о маркере или None если элемент не маркер
        """
        # Анализ <section> - только для structural и auto стратегий
        if isinstance(element, Section) and self.enable_structural:
            return self._analyze_section(element, parent_title_context)

        # Анализ <subtitle> - для structural/auto стратегий ИЛИ для специальной стратегии subtitle_star_separator
        if isinstance(element, Subtitle) and (self.enable_structural or self.enable_subtitle_star_separators):
            return self._analyze_subtitle(element)

        # Анализ <paragraph> - различные типы в зависимости от стратегии
        if isinstance(element, Paragraph):
            # Для стратегии subtitle_star_separator параграфы не анализируются как маркеры
            # Они просто добавляются в главы как контент
            if self.strategy_mode == "subtitle_star_separator":
                return None

            # Проверяем <strong> внутри параграфа (для strict и heuristic стратегий)
            if self.enable_strict_patterns or self.enable_heuristic_headers:
                strong_marker = self._analyze_paragraph_strong(element)
                if strong_marker:
                    return strong_marker

            # Проверяем <emphasis> внутри параграфа (для strict стратегии)
            if self.enable_strict_patterns:
                emphasis_marker = self._analyze_paragraph_emphasis(element)
                if emphasis_marker:
                    return emphasis_marker

            # Проверяем обычный текст параграфа (для strict стратегии)
            if self.enable_strict_patterns:
                text_marker = self._analyze_paragraph_text(element)
                if text_marker:
                    return text_marker

            # Проверяем разделители (только для auto стратегии)
            if self.strategy_mode == "auto":
                separator_marker = self._analyze_separators(element)
                if separator_marker:
                    return separator_marker

        # Анализ <empty-line> - может быть частью паттерна разделения глав
        if isinstance(element, EmptyLine):
            # EmptyLine сам по себе не является маркером, но может быть частью контекста
            # Возвращаем None, чтобы он просто добавился в текущую главу
            return None

        return None

    def _analyze_section(self, element: Section, parent_title_context: str = None) -> Optional[ChapterMarker]:
        """Анализирует элемент Section как потенциальный маркер главы.

        Section всегда считается сильным маркером главы, но если у секции нет
        заголовка, проверяем первый элемент содержимого на наличие эвристического заголовка.

        Args:
            element: Секция для анализа
            parent_title_context: Заголовок родительской секции для использования если у текущей секции нет заголовка
        """
        # Извлекаем заголовок секции с рекурсивной обработкой вложенных элементов
        title_text = ""
        if element.title and element.title.elements:
            # Используем утилитную функцию для извлечения заголовка
            from .utils import extract_clean_text

            title_text = extract_clean_text(element.title)

        # Если заголовок пустой, проверяем альтернативы
        if not title_text.strip():
            # Сначала пробуем использовать контекст родительского заголовка
            if parent_title_context and parent_title_context.strip():
                title_text = parent_title_context.strip()
            else:
                # Ищем эвристический заголовок в первом параграфе
                heuristic_title = self._extract_heuristic_title_from_section(element)
                if heuristic_title:
                    title_text = heuristic_title
                else:
                    title_text = "Глава"

        # Section всегда считается сильным маркером (вес 95)
        return ChapterMarker(type="SECTION", weight=95, text=title_text)

    def _analyze_subtitle(self, element: Subtitle) -> Optional[ChapterMarker]:
        """Анализирует элемент Subtitle."""
        text_parts = []
        for content_item in element.content:
            if isinstance(content_item, str):
                text_parts.append(content_item.strip())
        subtitle_text = " ".join(filter(None, text_parts)).strip()

        if not subtitle_text:
            # Для стратегии subtitle_star_separator пустые subtitle игнорируются
            if self.strategy_mode == "subtitle_star_separator":
                return None
            # Для остальных стратегий пустой subtitle - считаем маркером с средним весом
            return ChapterMarker(type="SUBTITLE", weight=70, text="Глава")

        # Специальная проверка для subtitle со звездным разделителем "* * *"
        # Но только для стратегии subtitle_star_separator или auto
        if self.enable_subtitle_star_separators and self._is_subtitle_star_separator(subtitle_text):
            # Высокий вес для четкого разделителя глав в subtitle
            return ChapterMarker(type="SUBTITLE_STAR_SEPARATOR", weight=80, text="")

        # Для стратегии subtitle_star_separator анализируем ТОЛЬКО звездные разделители
        if self.strategy_mode == "subtitle_star_separator":
            # Эта стратегия игнорирует обычные subtitle элементы
            return None

        # Для остальных стратегий проверяем соответствие паттернам
        if self._matches_chapter_pattern(subtitle_text):
            return ChapterMarker(type="SUBTITLE", weight=85, text=subtitle_text)
        else:
            # Любой subtitle считаем потенциальным маркером
            return ChapterMarker(type="SUBTITLE", weight=60, text=subtitle_text)

    def _analyze_paragraph_strong(self, element: Paragraph) -> Optional[ChapterMarker]:
        """Анализирует параграф с <strong> элементами.

        Применяет стратегии в зависимости от strategy_mode:
        - structural: строгие паттерны + числовые заголовки (вес 85-90)
        - strict: строгие паттерны + числовые заголовки (вес 85-90)
        - heuristic: строгие паттерны + эвристические правила + числовые (вес настраиваемый)
        - auto: все варианты
        """
        for content_item in element.content:
            if isinstance(content_item, Strong):
                text_attr = getattr(content_item, "text", "") or ""
                if isinstance(text_attr, str):
                    # Уровень 1: Строгие паттерны (для strict и heuristic стратегий)
                    if self.enable_strict_patterns:
                        # Проверяем строгие паттерны
                        if self._matches_chapter_pattern(text_attr):
                            return ChapterMarker(type="STRONG_HEADER", weight=90, text=text_attr)
                        # Проверяем числовые заголовки (1, 2, 3, ..., 21, 22, ...)
                        elif text_attr.strip().isdigit():
                            chapter_num = text_attr.strip()
                            return ChapterMarker(
                                type="STRONG_NUMERIC",
                                weight=85,
                                text=f"Глава {chapter_num}",
                            )

                    # Уровень 2: Эвристические правила (только для heuristic стратегии)
                    if self.enable_heuristic_headers and self._matches_heuristic_header(text_attr):
                        return ChapterMarker(
                            type="STRONG_HEURISTIC",
                            weight=self.heuristic_weight,
                            text=text_attr,
                        )
        return None

    def _analyze_paragraph_emphasis(self, element: Paragraph) -> Optional[ChapterMarker]:
        """Анализирует параграф с <emphasis> элементами."""
        for content_item in element.content:
            if isinstance(content_item, Emphasis):
                text_attr = getattr(content_item, "text", "") or ""
                if isinstance(text_attr, str) and self._matches_chapter_pattern(text_attr):
                    return ChapterMarker(type="EMPHASIS_HEADER", weight=75, text=text_attr)
        return None

    def _analyze_paragraph_text(self, element: Paragraph) -> Optional[ChapterMarker]:
        """Анализирует обычный текст параграфа."""
        text_parts = []
        for content_item in element.content:
            if isinstance(content_item, str):
                text_parts.append(content_item.strip())
        paragraph_text = " ".join(filter(None, text_parts))

        if self._matches_chapter_pattern(paragraph_text):
            # Специальная обработка для паттерна [N] - высокий вес и правильное название
            if re.match(self.BRACKET_PATTERN, paragraph_text):
                chapter_num = paragraph_text[1:-1]  # Извлекаем число из [N]
                return ChapterMarker(type="BRACKET_NUMERIC", weight=85, text=f"Глава {chapter_num}")
            # Специальная обработка для паттерна [[N]] - высокий вес и правильное название
            elif re.match(self.DOUBLE_BRACKET_PATTERN, paragraph_text):
                chapter_num = paragraph_text[2:-2]  # Извлекаем число из [[N]]
                return ChapterMarker(
                    type="DOUBLE_BRACKET_NUMERIC",
                    weight=85,
                    text=f"Глава {chapter_num}",
                )
            # Специальная обработка для паттерна [N] глава
            elif re.match(self.BRACKET_CHAPTER_PATTERN, paragraph_text, re.IGNORECASE):
                match = re.match(self.BRACKET_CHAPTER_PATTERN, paragraph_text, re.IGNORECASE)
                chapter_num = match.group(1)
                return ChapterMarker(type="BRACKET_CHAPTER", weight=85, text=f"Глава {chapter_num}")
            # Специальная обработка для паттерна chapter [N]
            elif re.match(self.CHAPTER_BRACKET_PATTERN, paragraph_text, re.IGNORECASE):
                match = re.match(self.CHAPTER_BRACKET_PATTERN, paragraph_text, re.IGNORECASE)
                chapter_num = match.group(1)
                return ChapterMarker(type="CHAPTER_BRACKET", weight=85, text=f"Chapter {chapter_num}")
            else:
                return ChapterMarker(type="TEXT_HEADER", weight=60, text=paragraph_text)
        return None

    def _analyze_separators(self, element: Paragraph) -> Optional[ChapterMarker]:
        """Анализирует разделители в параграфах."""
        text_parts = []
        for content_item in element.content:
            if isinstance(content_item, str):
                text_parts.append(content_item.strip())
        paragraph_text = " ".join(filter(None, text_parts)).strip()

        # Разделители: строки из звездочек, дефисов, знаков равенства
        # Звездочки с пробелами - высокий вес (частый маркер глав)
        star_separator_patterns = [
            r"^\*(\s+\*){2,}$",  # "* * *", "* * * * *"
        ]
        if any(re.match(pattern, paragraph_text) for pattern in star_separator_patterns):
            return ChapterMarker(type="STAR_SEPARATOR", weight=75, text="")

        # Другие разделители - низкий вес
        other_separator_patterns = [
            r"^\*{3,}$",  # ***
            r"^-{3,}$",  # ---
            r"^={3,}$",  # ===
        ]
        if any(re.match(pattern, paragraph_text) for pattern in other_separator_patterns):
            return ChapterMarker(type="SEPARATOR", weight=20, text="")
        return None

    def _matches_chapter_pattern(self, text: str) -> bool:
        """Проверяет соответствие текста паттернам заголовков глав."""
        if not text or not text.strip():
            return False

        text = text.strip()
        return any(pattern.match(text) for pattern in self._chapter_patterns)

    def _matches_heuristic_header(self, text: str) -> bool:
        """Эвристическая проверка на заголовок главы.

        Применяет набор правил для определения вероятности того,
        что текст является заголовком главы:
        - Ограничение по длине
        - Отсутствие знаков препинания в конце
        - Ограничение количества пунктуации
        - Первая буква заглавная
        - Не является числом

        Args:
            text: Текст для анализа

        Returns:
            True если текст похож на заголовок главы
        """
        if not text or not text.strip():
            return False

        text = text.strip()

        # Правило 1: Длина текста (короткие заголовки)
        if len(text) > self.max_header_length:
            return False

        # Правило 2: Отсутствие знаков препинания в конце
        if text.endswith((".", "!", "?", ":", ";")):
            return False

        # Правило 3: Не должен содержать много знаков препинания
        punctuation_count = sum(1 for c in text if c in ".,!?:;")
        if punctuation_count > 2:  # Слишком много пунктуации
            return False

        # Правило 4: Первая буква заглавная (опционально)
        if text and text[0].islower():
            return False

        # Правило 5: Не должен быть числом
        if text.isdigit():
            return False

        # Правило 6: Минимальная длина (слишком короткие не являются заголовками)
        if len(text) < 3:
            return False

        return True

    def _is_subtitle_star_separator(self, text: str) -> bool:
        """Проверяет, является ли subtitle звездным разделителем типа '* * *'.

        Распознает различные варианты звездных разделителей в subtitle элементах:
        - "* * *"
        - "* * * *"
        - "***"

        Args:
            text: Текст subtitle для проверки

        Returns:
            True если text является звездным разделителем
        """
        if not text or not text.strip():
            return False

        text = text.strip()

        # Паттерны звездных разделителей для subtitle
        star_separator_patterns = [
            r"^\*(\s+\*){2,}$",  # "* * *", "* * * * *" (с пробелами)
            r"^\*{3,}$",  # "***", "****" (без пробелов)
        ]

        return any(re.match(pattern, text) for pattern in star_separator_patterns)

    def _extract_heuristic_title_from_section(self, section: Section) -> Optional[str]:
        """Извлекает эвристический заголовок из первого элемента секции.

        Проверяет первый параграф секции на наличие Strong элемента,
        который может быть эвристическим заголовком.

        Args:
            section: Секция для анализа

        Returns:
            Текст заголовка или None если не найден
        """
        if not section.content:
            return None

        # Проверяем только первый элемент содержимого
        first_element = section.content[0]

        # Импортируем здесь чтобы избежать циклических импортов
        from .fb2_model import Paragraph

        if isinstance(first_element, Paragraph):
            # Анализируем параграф на наличие эвристического заголовка
            marker = self._analyze_paragraph_strong(first_element)
            if marker and marker.type == "STRONG_HEURISTIC":
                return marker.text

        return None

    def _has_internal_headers(self, content_elements: list) -> bool:
        """Проверяет, есть ли внутри содержимого потенциальные заголовки глав.

        Ищет <strong> элементы с числовыми значениями или строгими паттернами.

        Args:
            content_elements: Список элементов для проверки

        Returns:
            True если найдены потенциальные внутренние заголовки
        """
        header_count = 0
        found_headers = []

        for element in content_elements:
            if isinstance(element, Paragraph):
                # Проверяем <strong> элементы
                for content_item in element.content:
                    if isinstance(content_item, Strong):
                        text_attr = getattr(content_item, "text", "") or ""
                        if isinstance(text_attr, str):
                            text_clean = text_attr.strip()
                            # Проверяем числовые заголовки (1, 2, 3, ..., 21, 22, ...)
                            if text_clean.isdigit():
                                header_count += 1
                                found_headers.append(text_clean)

                            # Проверяем строгие паттерны
                            elif self._matches_chapter_pattern(text_clean):
                                header_count += 1
                                found_headers.append(text_clean)

                            # Если нашли достаточно заголовков, считаем что есть внутренняя структура
                            if header_count >= 3:
                                return True

        return False


class ChapterStateMachine:
    """Машина состояний для контекстно-зависимой обработки глав."""

    def __init__(
        self,
        markdown_renderer=None,
        strategy_mode: str = "auto",
        enable_heuristic_headers: bool = None,
    ):
        """Инициализирует машину состояний.

        Args:
            markdown_renderer: Инстанс MarkdownRenderer для преобразования контента
            strategy_mode: Режим стратегии ("structural", "strict", "heuristic", "auto")
            enable_heuristic_headers: DEPRECATED - для обратной совместимости
        """
        # Обратная совместимость: если передан старый параметр, конвертируем в strategy_mode
        if enable_heuristic_headers is not None:
            strategy_mode = "auto" if enable_heuristic_headers else "strict"
        self.current_state = ChapterState.SEARCHING
        self.chapters: list[CanonicalChapter] = []
        self.current_chapter_elements: list = []
        self.current_chapter_title = "Пролог"
        self.strategy_mode = strategy_mode
        self.marker_analyzer = MarkerAnalyzer(strategy_mode=strategy_mode, heuristic_weight=75, max_header_length=100)
        self.markdown_renderer = markdown_renderer

        # Статистика использованных маркеров для диагностики
        self.used_marker_types: set[str] = set()

    def process_element(self, element) -> None:
        """Обрабатывает один элемент книги в контексте текущего состояния.

        Args:
            element: FB2-элемент для обработки
        """
        # Специальная обработка для Section - рекурсивно обрабатываем содержимое
        if isinstance(element, Section):
            self._process_section(element)
            return

        # Анализируем элемент на предмет маркера главы
        marker = self.marker_analyzer.analyze(element)

        if marker is None:
            # Обычный элемент - добавляем в текущую главу
            self.current_chapter_elements.append(element)
            return

        # Есть маркер - принимаем решение на основе веса и текущего состояния
        if marker.weight >= 80:
            # Сильный маркер - однозначно новая глава
            self._finalize_previous_chapter()
            self._start_new_chapter(marker)
            self.used_marker_types.add(marker.type)
            self.current_state = ChapterState.IN_CHAPTER

        elif marker.weight >= 60:
            # Средний маркер - создаем новую главу (для эвристик)
            self._finalize_previous_chapter()
            self._start_new_chapter(marker)
            self.used_marker_types.add(marker.type)
            self.current_state = ChapterState.IN_CHAPTER

        elif marker.weight < 60:
            # Слабый маркер (например, разделители) - игнорируем как начало главы
            # Просто добавляем в текущую главу
            self.current_chapter_elements.append(element)

        else:
            # Средний маркер внутри главы - добавляем как обычный элемент
            # Это решает проблему с разделителями внутри глав
            self.current_chapter_elements.append(element)

    def _process_section(self, section: Section, parent_title_context: str = None) -> None:
        """Обрабатывает Section рекурсивно.

        1. Создает новую главу из Section (если есть прямое содержимое)
        2. Рекурсивно обрабатывает вложенные элементы

        Args:
            section: Секция для обработки
            parent_title_context: Заголовок родительской секции для использования в дочерних секциях без заголовка
        """
        # Получаем маркер для Section (может быть проигнорирован если есть внутренние заголовки)
        marker = self.marker_analyzer.analyze(section, parent_title_context)

        # Разделяем прямое содержимое и вложенные секции
        direct_content: list = []
        nested_sections = []

        # Добавляем эпиграфы секции в прямое содержимое
        if section.epigraphs:
            direct_content.extend(section.epigraphs)

        # Разделяем содержимое
        for item in section.content:
            if isinstance(item, Section):
                nested_sections.append(item)
            else:
                direct_content.append(item)

        # Создаем главу из прямого содержимого, если оно есть
        if direct_content:
            # Стандартная обработка - создаем главу из секции
            # Финализируем предыдущую главу
            self._finalize_previous_chapter()

            # Начинаем новую главу
            if marker:
                self._start_new_chapter(marker)
                self.used_marker_types.add(marker.type)
            else:
                # Fallback на случай, если маркер не создался
                fallback_marker = ChapterMarker(type="SECTION", weight=95, text="Глава")
                self._start_new_chapter(fallback_marker)
                self.used_marker_types.add(fallback_marker.type)

            # Обрабатываем прямое содержимое поэлементно (чтобы найти внутренние заголовки)
            for content_item in direct_content:
                if not isinstance(content_item, Section):  # Избегаем рекурсии
                    # Проверяем, является ли элемент маркером главы
                    inner_marker = self.marker_analyzer.analyze(content_item)
                    if inner_marker and inner_marker.weight >= 80:  # Числовые заголовки имеют вес 85
                        # Создаем новую главу для внутреннего заголовка
                        self._finalize_previous_chapter()
                        self._start_new_chapter(inner_marker)
                        self.used_marker_types.add(inner_marker.type)
                        self.current_state = ChapterState.IN_CHAPTER
                    else:
                        # Добавляем в текущую главу
                        self.current_chapter_elements.append(content_item)
                else:
                    # Для вложенных секций используем рекурсию
                    self.process_element(content_item)

            self.current_state = ChapterState.IN_CHAPTER

        # Рекурсивно обрабатываем вложенные секции
        # Определяем, нужно ли передать контекст заголовка дочерним секциям без заголовков
        parent_title_to_pass = None
        if (
            not direct_content  # Нет прямого контента
            and nested_sections  # Есть вложенные секции
            and marker
            and marker.text
            and marker.text != "Глава"
        ):  # Есть осмысленный заголовок
            parent_title_to_pass = marker.text

        # НОВАЯ ЛОГИКА: Передаем заголовок всем дочерним секциям без заголовков
        # до обнаружения первой секции с собственным заголовком
        title_still_available = parent_title_to_pass is not None
        for nested_section in nested_sections:
            context_to_pass = None

            # Проверяем, есть ли у секции собственный заголовок
            has_own_title = nested_section.title and nested_section.title.elements

            if title_still_available and not has_own_title:
                # Секция без заголовка - передаем родительский заголовок
                context_to_pass = parent_title_to_pass
            elif has_own_title:
                # Секция с заголовком - забываем родительский заголовок
                title_still_available = False

            self._process_section(nested_section, context_to_pass)

    def finalize(self) -> list[CanonicalChapter]:
        """Финализирует обработку и возвращает список глав.

        Returns:
            Список найденных глав
        """
        self._finalize_previous_chapter()
        return self.chapters

    def get_used_marker_types(self) -> set[str]:
        """Возвращает множество типов маркеров, которые были использованы для создания глав.

        Returns:
            Множество строк с типами маркеров (например: {'SECTION', 'STRONG_HEADER'})
        """
        return self.used_marker_types.copy()

    def _finalize_previous_chapter(self) -> None:
        """Сохраняет предыдущую главу если есть контент."""
        if self.current_chapter_elements:
            chapter = CanonicalChapter(
                title=self.current_chapter_title,
                content_elements=self.current_chapter_elements,
                _renderer=self.markdown_renderer,
            )
            self.chapters.append(chapter)
            logger.debug(
                f"Финализирована глава: '{self.current_chapter_title}' ({len(self.current_chapter_elements)} элементов)"
            )

    def _start_new_chapter(self, marker: ChapterMarker) -> None:
        """Начинает новую главу на основе маркера.

        Args:
            marker: Информация о маркере новой главы
        """
        self.current_chapter_elements = []
        self.current_chapter_title = marker.text.strip() if marker.text.strip() else f"Глава {len(self.chapters) + 1}"
        logger.debug(f"Начата новая глава: '{self.current_chapter_title}' (тип: {marker.type}, вес: {marker.weight})")
