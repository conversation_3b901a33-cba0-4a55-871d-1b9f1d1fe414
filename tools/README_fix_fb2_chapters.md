# Инструмент исправления глав в FB2 книгах

## Описание

`tools/fix_fb2_chapters.py` - инструмент для автоматического исправления структуры глав в FB2 файлах на основе предоставленных данных о главах. Поддерживает работу с архивами и сохранение оригинальной даты создания файлов.

## Возможности

✅ **JSON конфигурация** - удобная работа с конфигурационными файлами  
✅ **Проверка состояния** - автоматическая проверка существующих глав перед обработкой  
✅ **Идемпотентность** - безопасный повторный запуск на уже обработанных файлах  
  
✅ **Работа с архивами** - извлечение FB2 файлов из ZIP архивов  
✅ **Умный поиск глав** - поиск содержимого глав по первым словам с учетом невидимых символов  
✅ **Создание структуры** - автоматическое создание правильной XML структуры глав  
✅ **Валидация XML** - проверка корректности созданного XML  
✅ **Сохранение даты** - сохранение оригинальной даты создания архива  
✅ **Автоочистка** - автоматическое удаление временных файлов  

## Использование

### Основной способ - JSON конфигурация (рекомендуется)

```bash
python tools/fix_fb2_chapters.py --config tmp/config.json
```

### Дополнительные режимы

```bash
# Принудительная обработка (игнорирует все проверки)
python tools/fix_fb2_chapters.py --config tmp/config.json --force

# Работа только с существующими временными файлами
python tools/fix_fb2_chapters.py --config tmp/config.json --resume

# Очистка временных файлов
python tools/fix_fb2_chapters.py --config tmp/config.json --cleanup
```



## Формат JSON конфигурации

Создайте файл в папке `tmp/` (например, `tmp/chapters_config.json`):

```json
{
  "file_path": "/mnt/storage/books/zip/archive.zip::book.fb2",
  "chapters": [
    {
      "chapter_title": "Глава 1. Королевский подарок и суровая реальность",
      "chapter_first_words": "В старину Балтийское море называли"
    },
    {
      "chapter_title": "Глава 2. Рейд на Дерпт и дуэль поэтов",
      "chapter_first_words": "Потешив душеньку разбоем, я, следуя"
    }
  ]
}
```

**Важно**: Храните JSON файлы в папке `tmp/` - они не попадут в репозиторий.

## Умная логика работы

Инструмент автоматически определяет что делать:

### 🔍 **Проверка временных файлов**
- Есть `*_fixed.fb2`? → Проверяет и упаковывает
- Нет временных файлов? → Обычная обработка

### 📊 **Проверка состояния архива**
- ✅ **Файл уже обработан** - если количество и названия глав совпадают
- 🔄 **Требуется обработка** - если структура глав отличается

### ⚠️ **Обработка ошибок**
- XML невалидный? → Сохраняет временные файлы для ручной правки
- После ручного исправления → Автоматически упаковывает при следующем запуске

### Примеры работы умной логики:

**Обычная обработка:**
```
📋 Загружена конфигурация: 12 глав
🔍 Проверка текущего состояния файла в архиве...
📊 Найдено глав в архиве: 0
🔄 Начинаем обработку глав...
✅ XML валидный
🎉 Главы успешно исправлены!
```

**Файл уже обработан:**
```
📊 Найдено глав в архиве: 12
✅ Файл уже содержит корректную структуру глав!
📋 Совпадающих заголовков: 12/12
```

**Ошибка XML - сохранение для ручной правки:**
```
❌ XML невалидный: mismatched tag: line 1401
💡 Временные файлы сохранены для ручной правки:
   Исходный: tmp/fix_chapters_xxx.fb2
   Исправленный: tmp/fix_chapters_xxx_fixed.fb2
💡 После ручного исправления запустите команду снова
```

**Повторный запуск после ручной правки:**
```
🔍 Найден временный исправленный файл: tmp/fix_chapters_xxx_fixed.fb2
✅ Временный файл валидный и содержит правильную структуру
🎉 Файл успешно упакован из временного!
```

## Алгоритм работы

1. **Загрузка конфигурации** - из JSON файла или промпта
2. **Проверка состояния** - анализ существующих глав (если не --force)
3. **Извлечение из архива** - создание временного файла FB2
4. **Анализ структуры** - поиск основной секции `<body>`
5. **Поиск глав** - нахождение содержимого каждой главы по первым словам
6. **Создание XML** - формирование правильной структуры глав
7. **Валидация** - проверка корректности XML
8. **Сохранение** - замена файла в архиве с сохранением даты
9. **Очистка** - удаление временных файлов

## Особенности поиска глав

- Очистка от невидимых символов (неразрывные пробелы, переносы строк)
- Поиск по первым 4-6 словам для точности
- Определение границ глав по началу следующей главы
- Обработка последней главы до конца секции

## Примеры использования

### Создание конфигурации

```bash
# Создаем JSON конфигурацию
cat > tmp/my_book_config.json << 'EOF'
{
  "file_path": "/mnt/storage/books/zip/book.zip::book.fb2",
  "chapters": [
    {
      "chapter_title": "Глава 1. Начало",
      "chapter_first_words": "В старину люди верили"
    }
  ]
}
EOF
```

### Обработка

```bash
# Первый запуск - обработка
python tools/fix_fb2_chapters.py --config tmp/my_book_config.json

# Повторный запуск - проверка состояния
python tools/fix_fb2_chapters.py --config tmp/my_book_config.json

# Принудительная обработка
python tools/fix_fb2_chapters.py --config tmp/my_book_config.json --force
```

## Проверка результата

```bash
# Проверка диагностическим инструментом
python tools/run_diagnostic_tool.py --input /path/to/archive.zip::file.fb2
```

## Требования

- Python 3.12+
- Модули: `xml.etree.ElementTree`, `zipfile`, `pathlib`, `re`, `json`
- Права на запись в директорию `tmp/`
- Доступ к исходному архиву для чтения и записи

## Структура файлов

```
tools/
├── fix_fb2_chapters.py          # Основной инструмент
└── README_fix_fb2_chapters.md   # Документация

tmp/
├── chapters_config.json         # JSON конфигурации (не в репе)
└── fix_chapters_*              # Временные файлы (автоудаление)
```