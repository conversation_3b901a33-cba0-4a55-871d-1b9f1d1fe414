#!/usr/bin/env python3
"""
🔧 Инструмент для исправления глав в FB2 книгах

Исправляет структуру глав в FB2 файлах на основе JSON конфигурации.
Поддерживает работу с архивами и сохранение оригинальной даты создания.

Использование:
python tools/fix_fb2_chapters.py --config tmp/config.json
python tools/fix_fb2_chapters.py --config tmp/config.json --force
"""

import argparse
import hashlib
import json
import os
import re
import shutil
import tempfile
import xml.etree.ElementTree as ET
import zipfile
from pathlib import Path
from typing import Any


class FB2ChapterFixer:
    def __init__(self, work_dir="tmp"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)

    def load_config_from_json(self, config_path: str) -> tuple[str, list[dict[str, str]]]:
        """Загружает конфигурацию из JSON файла"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            file_path = config.get("file_path", "")
            chapters_data = config.get("chapters", [])

            if not file_path:
                raise ValueError("В конфигурации отсутствует поле 'file_path'")
            if not chapters_data:
                raise ValueError("В конфигурации отсутствует поле 'chapters' или оно пустое")

            print(f"📋 Загружена конфигурация: {len(chapters_data)} глав")
            return file_path, chapters_data

        except (json.JSONDecodeError, FileNotFoundError) as e:
            raise ValueError(f"Ошибка загрузки конфигурации: {e}") from e

    def check_existing_chapters(self, fb2_file: Path, expected_chapters: list[dict[str, str]]) -> dict:
        """Проверяет существующие главы в файле"""
        try:
            with open(fb2_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Ищем существующие главы с заголовками (включая Эпилог, Пролог и т.д.)
            chapter_pattern = (
                r"<section>\s*<title>\s*<p>((?:Глава|Эпилог|Пролог|Введение|Заключение)[^<]*)</p>\s*</title>"
            )
            existing_titles = re.findall(chapter_pattern, content, re.IGNORECASE | re.DOTALL)

            # Очищаем найденные заголовки от лишних пробелов
            existing_titles = [title.strip() for title in existing_titles]

            # Получаем ожидаемые заголовки
            expected_titles = [ch["chapter_title"].strip() for ch in expected_chapters]

            match_details: list[dict[str, Any]] = []
            analysis = {
                "existing_count": len(existing_titles),
                "expected_count": len(expected_titles),
                "existing_titles": existing_titles,
                "expected_titles": expected_titles,
                "already_processed": False,
                "match_details": match_details,
            }

            # Проверяем совпадения
            matches = 0
            for i, expected_title in enumerate(expected_titles):
                if i < len(existing_titles):
                    existing_title = existing_titles[i]
                    is_match = existing_title == expected_title
                    if is_match:
                        matches += 1
                    match_details.append(
                        {
                            "index": i + 1,
                            "expected": expected_title,
                            "existing": existing_title,
                            "match": is_match,
                        }
                    )
                else:
                    match_details.append(
                        {
                            "index": i + 1,
                            "expected": expected_title,
                            "existing": None,
                            "match": False,
                        }
                    )

            # Считаем файл обработанным если:
            # 1. Количество глав совпадает
            # 2. Большинство заголовков совпадают (>= 80%)
            if analysis["existing_count"] == analysis["expected_count"] and matches >= len(expected_titles) * 0.8:
                analysis["already_processed"] = True

            return analysis

        except Exception as e:
            return {
                "error": str(e),
                "already_processed": False,
                "existing_count": 0,
                "expected_count": len(expected_chapters),
            }

    def extract_fb2_from_archive(self, archive_path: str, file_in_archive: str) -> tuple[Path, str]:
        """Извлекает FB2 файл из архива во временную директорию"""

        # Генерируем уникальный ID для временного файла
        file_id = hashlib.md5(f"{archive_path}::{file_in_archive}".encode(), usedforsecurity=False).hexdigest()[:8]
        tmp_file = self.work_dir / f"fix_chapters_{file_id}_{Path(file_in_archive).name}"

        try:
            with zipfile.ZipFile(archive_path, "r") as zip_ref:
                with zip_ref.open(file_in_archive) as source:
                    with open(tmp_file, "wb") as target:
                        target.write(source.read())

            print(f"📁 Файл извлечен из архива: {tmp_file}")
            return tmp_file, file_id

        except (zipfile.BadZipFile, KeyError) as e:
            raise ValueError(f"Ошибка извлечения файла из архива: {e}") from e

    def clean_text_for_comparison(self, text: str) -> str:
        """Очищает текст от невидимых символов и лишних пробелов для сравнения"""
        # Убираем XML теги
        text = re.sub(r"<[^>]+>", "", text)
        # Убираем все невидимые символы (включая неразрывные пробелы, переносы строк и т.д.)
        text = re.sub(r"[\u00A0\u2000-\u200F\u2028-\u202F\u205F-\u206F\uFEFF\r\n\t]", " ", text)
        # Убираем лишние пробелы и символы пунктуации для поиска
        text = re.sub(r"[^\w\s]", " ", text)
        # Нормализуем пробелы
        text = re.sub(r"\s+", " ", text).strip()
        return text.lower()

    def find_chapter_content(self, content: str, first_words: str, next_first_words: str = None) -> tuple[int, int]:
        """Находит начало и конец главы по первым словам"""

        # Очищаем первые слова для поиска
        clean_first_words = self.clean_text_for_comparison(first_words)
        search_words = clean_first_words.split()[:6]  # Берем первые 6 слов для точности

        if len(search_words) < 2:
            print(f"⚠️ Слишком мало слов для поиска: {first_words}")
            return None, None

        # Ищем эти слова в тексте
        lines = content.split("\n")
        start_line = None

        print(f"🔍 Ищем слова: {' '.join(search_words[:4])}")

        for i, line in enumerate(lines):
            clean_line = self.clean_text_for_comparison(line)

            # Проверяем, содержит ли строка первые слова главы
            if len(search_words) >= 3:
                # Ищем первые 4 слова подряд
                search_phrase = " ".join(search_words[:4])
                if clean_line.startswith(search_phrase) or search_phrase in clean_line:
                    start_line = i
                    print(f"✅ Найдено совпадение на строке {i + 1}: {line[:100]}...")
                    break

                # Если не найдено, ищем первые 3 слова
                search_phrase = " ".join(search_words[:3])
                if clean_line.startswith(search_phrase):
                    start_line = i
                    print(f"✅ Найдено частичное совпадение на строке {i + 1}: {line[:100]}...")
                    break

        if start_line is None:
            print(f"❌ Не найдено содержимое для слов: {first_words[:50]}...")
            return None, None

        # Ищем конец главы - начало следующей главы или конец секции
        end_line = len(lines)

        if next_first_words:
            # Ищем начало следующей главы
            next_clean_words = self.clean_text_for_comparison(next_first_words)
            next_words = next_clean_words.split()[:4]

            print(f"🔍 Ищем конец главы по началу следующей: {' '.join(next_words[:3])}")

            for i in range(start_line + 10, len(lines)):  # Начинаем поиск через 10 строк
                clean_line = self.clean_text_for_comparison(lines[i])

                if len(next_words) >= 3:
                    search_phrase = " ".join(next_words[:3])
                    if clean_line.startswith(search_phrase) or search_phrase in clean_line:
                        end_line = i
                        print(f"✅ Найден конец главы на строке {i + 1}")
                        break
        else:
            # Это последняя глава - ищем конец секции или конец body
            print("🔍 Ищем конец последней главы")
            for i in range(start_line + 1, len(lines)):
                line = lines[i].strip()
                if ("</section>" in line or "</body>" in line) and "notes" not in line:
                    end_line = i
                    print(f"✅ Найден конец главы на строке {i + 1}: {line}")
                    break

        print(f"📍 Глава найдена: строки {start_line + 1}-{end_line + 1}")
        return start_line, end_line

    def create_chapter_xml(self, title: str, content_lines: list[str]) -> str:
        """Создает XML структуру для главы"""

        xml_lines = [
            "  <section>",
            "    <title>",
            f"      <p>{self.escape_xml(title)}</p>",
            "    </title>",
        ]

        # Добавляем содержимое главы
        for line in content_lines:
            line = line.strip()
            # Пропускаем структурные теги секций, заголовки и пустые строки
            if (
                line
                and not line.startswith("<section")
                and not line == "</section>"
                and not line.startswith("<title")
                and not line == "</title>"
            ):
                # Если строка уже содержит теги параграфа, используем как есть
                if line.startswith("<p>") and line.endswith("</p>"):
                    xml_lines.append(f"    {line}")
                elif "<p>" in line:
                    # Строка содержит теги параграфа, но может быть неполной
                    xml_lines.append(f"    {line}")
                else:
                    # Убираем XML теги и оборачиваем в параграф
                    clean_line = re.sub(r"<[^>]+>", "", line).strip()
                    if clean_line:
                        xml_lines.append(f"    <p>{self.escape_xml(clean_line)}</p>")

        xml_lines.append("  </section>")
        return "\n".join(xml_lines)

    def escape_xml(self, text: str) -> str:
        """Экранирует специальные символы для XML"""
        return (
            text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&apos;")
        )

    def fix_fb2_chapters(self, fb2_file: Path, chapters_data: list[dict[str, str]]) -> Path:
        """Исправляет структуру глав в FB2 файле - ПРОСТОЕ РЕШЕНИЕ"""

        # Читаем весь файл как текст
        with open(fb2_file, "r", encoding="utf-8") as f:
            content = f.read()

        print(f"📄 Работаем с файлом: {len(content)} символов")

        # Проверяем какие главы уже есть в файле (включая Эпилог, Пролог и т.д.)
        existing_titles = re.findall(
            r"<section>\s*<title>\s*<p>((?:Глава|Эпилог|Пролог|Введение|Заключение)[^<]*)</p>\s*</title>",
            content,
            re.IGNORECASE | re.DOTALL,
        )
        existing_titles = [title.strip() for title in existing_titles]

        print(f"📊 Найдено существующих глав: {len(existing_titles)}")
        for title in existing_titles:
            print(f"  ✓ {title}")

        # Определяем какие главы нужно добавить
        missing_chapters = []
        for orig_index, chapter_data in enumerate(chapters_data):
            expected_title = chapter_data.get("chapter_title", "").strip()
            if expected_title not in existing_titles:
                chapter_with_index = chapter_data.copy()
                chapter_with_index["_original_index"] = str(orig_index)
                missing_chapters.append(chapter_with_index)
                print(f"❌ Отсутствует: {expected_title}")
            else:
                print(f"✅ Уже есть: {expected_title}")

        if not missing_chapters:
            print("🎉 Все главы уже корректно оформлены!")
            return fb2_file

        print(f"🔄 Нужно добавить {len(missing_chapters)} глав")

        # Сначала находим все позиции недостающих глав
        replacements = []
        for i, chapter_data in enumerate(missing_chapters):
            title = chapter_data.get("chapter_title", "Без названия")
            first_words = chapter_data.get("chapter_first_words", "")

            if not first_words:
                print(f"⚠️ Пропущена глава без первых слов: {title}")
                continue

            # Ищем разные варианты начала главы
            search_variants = [
                f"<p>{first_words}",
                f"<p>— {first_words}",
                f"<p>{first_words[:25]}",
            ]

            pos = -1
            used_text = ""
            for variant in search_variants:
                pos = content.find(variant)
                if pos != -1:
                    used_text = variant
                    break

            if pos != -1:
                print(f"✅ Найдена глава: {title}")
                orig_idx = chapter_data.get("_original_index", str(i))
                # Преобразуем в int для совместимости типов
                orig_idx_int = int(orig_idx) if isinstance(orig_idx, str) else orig_idx
                replacements.append((pos, len(used_text), title, used_text, orig_idx_int))
            else:
                print(f"❌ Не найдена глава: {title}")

        # Применяем замены в обратном порядке (с конца файла)
        replacements.sort(reverse=True)
        for pos, orig_len, title, used_text, i in replacements:
            if i == 0:
                # Первая глава - заменяем <p> на секцию
                replacement = f"<section>\n  <title>\n    <p>{self.escape_xml(title)}</p>\n  </title>\n  {used_text}"
                content = content[:pos] + replacement + content[pos + orig_len :]
            else:
                # Остальные главы - закрываем предыдущую и открываем новую
                replacement = (
                    f"</section>\n<section>\n  <title>\n    <p>{self.escape_xml(title)}</p>\n  </title>\n  {used_text}"
                )
                content = content[:pos] + replacement + content[pos + orig_len :]

        # Закрываем последнюю секцию перед </body>
        body_end_pos = content.rfind("</body>")
        if body_end_pos != -1:
            content = content[:body_end_pos] + "</section>\n" + content[body_end_pos:]

        # Создаем исправленный файл
        fixed_file = fb2_file.parent / f"{fb2_file.stem}_fixed.fb2"
        with open(fixed_file, "w", encoding="utf-8") as f:
            f.write(content)

        print(f"🔧 Создан исправленный файл: {fixed_file}")
        print(f"📊 Добавлено недостающих глав: {len(missing_chapters)}")

        return fixed_file

    def validate_fb2(self, fb2_file: Path) -> bool:
        """Проверяет валидность FB2 файла"""
        try:
            ET.parse(fb2_file)  # nosec B314 - инструментальный код для анализа локальных файлов
            print(f"✅ XML валидный: {fb2_file}")
            return True
        except ET.ParseError as e:
            print(f"❌ XML невалидный: {e}")
            return False

    def save_to_archive_with_date(self, archive_path: str, file_in_archive: str, fixed_file: Path) -> bool:
        """Сохраняет исправленный файл обратно в архив с сохранением даты архива и файла"""

        try:
            # Сохраняем оригинальную дату архива
            original_archive_stat = os.stat(archive_path)

            # Сохраняем оригинальную дату файла в архиве
            original_file_info = None
            with zipfile.ZipFile(archive_path, "r") as original_zip:
                for item in original_zip.infolist():
                    if item.filename == file_in_archive:
                        original_file_info = item
                        break

            if not original_file_info:
                raise ValueError(f"Файл {file_in_archive} не найден в архиве")

            # Создаем временную копию архива
            with tempfile.NamedTemporaryFile(delete=False, suffix=".zip") as temp_archive:
                temp_archive_path = temp_archive.name

            # Копируем архив и заменяем файл
            with zipfile.ZipFile(archive_path, "r") as original_zip:
                with zipfile.ZipFile(temp_archive_path, "w", compression=zipfile.ZIP_DEFLATED) as new_zip:
                    # Копируем все файлы кроме заменяемого
                    for item in original_zip.infolist():
                        if item.filename != file_in_archive:
                            data = original_zip.read(item.filename)
                            new_zip.writestr(item, data)

                    # Добавляем исправленный файл с сохранением оригинальной даты
                    with open(fixed_file, "rb") as f:
                        file_data = f.read()

                    # Создаем новый ZipInfo с оригинальными датами
                    new_info = zipfile.ZipInfo(filename=file_in_archive)
                    new_info.date_time = original_file_info.date_time
                    new_info.compress_type = zipfile.ZIP_DEFLATED

                    # Записываем файл с сохранением даты
                    new_zip.writestr(new_info, file_data)

            # Заменяем оригинальный архив
            shutil.move(temp_archive_path, archive_path)

            # Восстанавливаем оригинальную дату архива
            os.utime(
                archive_path,
                (original_archive_stat.st_atime, original_archive_stat.st_mtime),
            )

            print(f"✅ Файл сохранен в архив: {archive_path}::{file_in_archive}")
            print(f"📅 Дата архива сохранена: {original_archive_stat.st_mtime}")
            print(f"📅 Дата файла сохранена: {original_file_info.date_time}")

            return True

        except Exception as e:
            print(f"❌ Ошибка сохранения в архив: {e}")
            return False

    def cleanup_temp_files(self, file_id: str):
        """Удаляет временные файлы"""
        temp_files = list(self.work_dir.glob(f"fix_chapters_{file_id}*"))
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                print(f"🗑️ Удален временный файл: {temp_file}")
            except Exception as e:
                print(f"⚠️ Не удалось удалить {temp_file}: {e}")

    def check_existing_temp_files(self, file_id: str, file_name: str) -> dict:
        """Проверяет существующие временные файлы"""
        original_file = self.work_dir / f"fix_chapters_{file_id}_{file_name}"
        fixed_file = self.work_dir / f"fix_chapters_{file_id}_{file_name.replace('.fb2', '_fixed.fb2')}"

        return {
            "original_exists": original_file.exists(),
            "fixed_exists": fixed_file.exists(),
            "original_file": original_file,
            "fixed_file": fixed_file,
        }

    def process_existing_fixed_file(
        self,
        fixed_file: Path,
        chapters_data: list[dict[str, str]],
        archive_path: str,
        file_in_archive: str,
        file_id: str,
        save: bool = False,
    ) -> bool:
        """Обрабатывает существующий исправленный файл"""
        print(f"🔍 Найден временный исправленный файл: {fixed_file}")

        # Проверяем валидность
        if not self.validate_fb2(fixed_file):
            print("❌ Временный файл невалидный")
            print(f"💡 Исправьте файл вручную: {fixed_file}")
            print("💡 После исправления запустите команду снова")
            return False

        # Проверяем структуру глав
        analysis = self.check_existing_chapters(fixed_file, chapters_data)
        if analysis.get("error"):
            print(f"⚠️ Ошибка анализа временного файла: {analysis['error']}")
        else:
            print(f"📊 Найдено глав в временном файле: {analysis['existing_count']}")
            print(f"📊 Ожидается глав: {analysis['expected_count']}")

            if not analysis["already_processed"]:
                print("⚠️ Структура глав в временном файле не соответствует ожидаемой")

                # Детальный анализ различий
                print("\n🔍 Детальный анализ:")
                existing_set = set(analysis["existing_titles"])
                expected_set = set(analysis["expected_titles"])

                missing = expected_set - existing_set
                extra = existing_set - expected_set

                if missing:
                    print(f"❌ Отсутствуют главы ({len(missing)}):")
                    for title in sorted(missing):
                        print(f"  - {title}")

                if extra:
                    print(f"⚠️ Лишние главы ({len(extra)}):")
                    for title in sorted(extra):
                        print(f"  + {title}")

                print(f"\n💡 Исправьте файл вручную: {fixed_file}")
                return False

        # Все проверки пройдены - решаем что делать
        print("✅ Временный файл валидный и содержит правильную структуру")

        if save:
            if self.save_to_archive_with_date(archive_path, file_in_archive, fixed_file):
                print("🎉 Файл успешно упакован из временного!")
                self.cleanup_temp_files(file_id)
                return True
            else:
                print("❌ Не удалось сохранить в архив")
                return False
        else:
            print(f"📁 Временный исправленный файл: {fixed_file}")
            print("💡 Для сохранения в архив используйте --save")
            print("python tools/fix_fb2_chapters.py --config tmp/chapters_config.json --save")
            return True

    def process_fb2_chapters(
        self,
        config_path: str,
        force: bool = False,
        resume: bool = False,
        cleanup: bool = False,
        save: bool = False,
    ) -> bool:
        """Основной метод обработки FB2 файла с главами"""

        try:
            # Загружаем конфигурацию из JSON
            file_path, chapters_data = self.load_config_from_json(config_path)

            print(f"📖 Обработка файла: {file_path}")
            print(f"📊 Количество глав: {len(chapters_data)}")

            # Проверяем формат пути
            if "::" not in file_path:
                raise ValueError("Поддерживаются только файлы в архивах (формат: архив.zip::файл.fb2)")

            archive_path, file_in_archive = file_path.split("::", 1)

            # Проверяем существование архива
            if not Path(archive_path).exists():
                raise ValueError(f"Архив не найден: {archive_path}")

            # Генерируем ID для временных файлов
            file_id = hashlib.md5(file_path.encode(), usedforsecurity=False).hexdigest()[:8]
            file_name = Path(file_in_archive).name

            # Режим очистки
            if cleanup:
                print("🧹 Режим очистки временных файлов...")
                self.cleanup_temp_files(file_id)
                print("✅ Временные файлы удалены")
                return True

            # Проверяем существующие временные файлы
            temp_status = self.check_existing_temp_files(file_id, file_name)

            # Если есть исправленный файл - работаем с ним
            if temp_status["fixed_exists"] and not force:
                return self.process_existing_fixed_file(
                    temp_status["fixed_file"],
                    chapters_data,
                    archive_path,
                    file_in_archive,
                    file_id,
                    save,
                )

            # Режим resume - только с существующими файлами
            if resume:
                if not temp_status["fixed_exists"]:
                    print("❌ Режим --resume требует существующий временный файл")
                    print(f"💡 Не найден файл: {temp_status['fixed_file']}")
                    return False
                return self.process_existing_fixed_file(
                    temp_status["fixed_file"],
                    chapters_data,
                    archive_path,
                    file_in_archive,
                    file_id,
                    save,
                )

            # Извлекаем файл из архива (если еще нет)
            if temp_status["original_exists"]:
                print(f"📁 Используется существующий временный файл: {temp_status['original_file']}")
                tmp_file = temp_status["original_file"]
            else:
                tmp_file, _ = self.extract_fb2_from_archive(archive_path, file_in_archive)

            try:
                # Проверяем текущее состояние файла в архиве (не временного)
                if not force and not temp_status["original_exists"]:
                    print("🔍 Проверка текущего состояния файла в архиве...")
                    analysis = self.check_existing_chapters(tmp_file, chapters_data)

                    if analysis.get("error"):
                        print(f"⚠️ Ошибка анализа: {analysis['error']}")
                    else:
                        print(f"📊 Найдено глав в архиве: {analysis['existing_count']}")
                        print(f"📊 Ожидается глав: {analysis['expected_count']}")

                        if analysis["already_processed"]:
                            print("✅ Файл в архиве уже содержит корректную структуру глав!")
                            print("💡 Используйте --force для принудительной обработки")

                            # Показываем детали совпадений
                            matches = sum(1 for detail in analysis["match_details"] if detail["match"])
                            print(f"📋 Совпадающих заголовков: {matches}/{analysis['expected_count']}")

                            # Удаляем временный файл если он был создан
                            if tmp_file.exists():
                                tmp_file.unlink()
                            return True

                # Исправляем главы
                print("🔄 Начинаем обработку глав...")
                fixed_file = self.fix_fb2_chapters(tmp_file, chapters_data)

                # Проверяем валидность
                if not self.validate_fb2(fixed_file):
                    print("❌ Исправленный файл невалидный")
                    print("💡 Временные файлы сохранены для ручной правки:")
                    print(f"   Исходный: {tmp_file}")
                    print(f"   Исправленный: {fixed_file}")
                    print("💡 После ручного исправления запустите команду снова")
                    return False

                # Сохраняем обратно в архив только если указан --save
                if save:
                    if self.save_to_archive_with_date(archive_path, file_in_archive, fixed_file):
                        print("🎉 Главы успешно исправлены и сохранены в архив!")
                        # Удаляем временные файлы только при успехе
                        self.cleanup_temp_files(file_id)
                        return True
                    else:
                        print("❌ Не удалось сохранить в архив")
                        print(f"💡 Временные файлы сохранены: {tmp_file}, {fixed_file}")
                        return False
                else:
                    print("🎉 Главы успешно исправлены!")
                    print("📁 Временные файлы для проверки:")
                    print(f"   Исходный: {tmp_file}")
                    print(f"   Исправленный: {fixed_file}")
                    print("💡 Для сохранения в архив используйте --save")
                    return True

            except Exception as e:
                print(f"❌ Ошибка обработки: {e}")
                print("💡 Временные файлы сохранены для отладки")
                return False

        except Exception as e:
            print(f"❌ Ошибка обработки: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description="Исправление глав в FB2 книгах")
    parser.add_argument("--config", required=True, help="Путь к JSON файлу с конфигурацией")
    parser.add_argument(
        "--force",
        action="store_true",
        help="Принудительная обработка (игнорировать проверки)",
    )
    parser.add_argument(
        "--resume",
        action="store_true",
        help="Работа только с существующими временными файлами",
    )
    parser.add_argument("--cleanup", action="store_true", help="Удаление временных файлов")
    parser.add_argument(
        "--save",
        action="store_true",
        help="Сохранить в архив и удалить временные файлы",
    )

    args = parser.parse_args()

    fixer = FB2ChapterFixer()
    success = fixer.process_fb2_chapters(
        config_path=args.config,
        force=args.force,
        resume=args.resume,
        cleanup=args.cleanup,
        save=args.save,
    )

    if success:
        print("\n✅ Задача выполнена успешно!")
    else:
        print("\n❌ Задача завершилась с ошибками")
        exit(1)


if __name__ == "__main__":
    main()
